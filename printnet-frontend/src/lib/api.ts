import axios, { type AxiosInstance, type AxiosResponse } from "axios";
import type {
  AuthResponse,
  LoginRequest,
  RegisterRequest,
  User,
  Order,
  VendingMachine,
  Payment,
  ApiResponse,
  PaginatedResponse,
  CreateOrderRequest,
  OrderFilters,
  UserFilters,
  DashboardData,
  DashboardAlert,
} from "@/types";

class ApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: import.meta.env.VITE_API_URL || "http://localhost:3000",
      headers: {
        "Content-Type": "application/json",
      },
    });

    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem("auth_token");
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem("auth_token");
          window.location.href = "/login";
        }
        return Promise.reject(error);
      }
    );
  }

  // Authentication endpoints
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    const response: AxiosResponse<ApiResponse<AuthResponse>> =
      await this.client.post("/auth/login", credentials);
    return response.data.data;
  }

  async register(userData: RegisterRequest): Promise<ApiResponse<User>> {
    const response: AxiosResponse<ApiResponse<User>> = await this.client.post(
      "/auth",
      userData
    );
    return response.data;
  }

  // User endpoints
  async getProfile(): Promise<ApiResponse<User>> {
    const response: AxiosResponse<ApiResponse<User>> = await this.client.get(
      "/users/profile"
    );
    return response.data;
  }

  async getUsers(
    filters?: UserFilters
  ): Promise<ApiResponse<PaginatedResponse<User>>> {
    const response: AxiosResponse<ApiResponse<PaginatedResponse<User>>> =
      await this.client.get("/users", {
        params: filters,
      });
    return response.data;
  }

  async getUserById(id: string): Promise<ApiResponse<User>> {
    const response: AxiosResponse<ApiResponse<User>> = await this.client.get(
      `/users/${id}`
    );
    return response.data;
  }

  async updateUser(
    id: string,
    userData: Partial<User>
  ): Promise<ApiResponse<User>> {
    const response: AxiosResponse<ApiResponse<User>> = await this.client.put(
      `/users/${id}`,
      userData
    );
    return response.data;
  }

  async deactivateUser(id: string): Promise<ApiResponse<void>> {
    const response: AxiosResponse<ApiResponse<void>> = await this.client.delete(
      `/users/${id}`
    );
    return response.data;
  }

  async activateUser(id: string): Promise<ApiResponse<User>> {
    const response: AxiosResponse<ApiResponse<User>> = await this.client.post(
      `/users/${id}/activate`
    );
    return response.data;
  }

  async assignRole(userId: string, role: string): Promise<ApiResponse<void>> {
    const response: AxiosResponse<ApiResponse<void>> = await this.client.post(
      `/users/${userId}/roles`,
      { role }
    );
    return response.data;
  }

  // Order endpoints
  async getOrders(
    filters?: OrderFilters
  ): Promise<ApiResponse<PaginatedResponse<Order>>> {
    const response: AxiosResponse<ApiResponse<PaginatedResponse<Order>>> =
      await this.client.get("/orders", {
        params: filters,
      });
    return response.data;
  }

  async getOrderById(id: string): Promise<ApiResponse<Order>> {
    const response: AxiosResponse<ApiResponse<Order>> = await this.client.get(
      `/orders/${id}`
    );
    return response.data;
  }

  async createOrder(
    orderData: CreateOrderRequest
  ): Promise<ApiResponse<Order>> {
    const response: AxiosResponse<ApiResponse<Order>> = await this.client.post(
      "/orders",
      orderData
    );
    return response.data;
  }

  async updateOrder(
    id: string,
    orderData: Partial<Order>
  ): Promise<ApiResponse<Order>> {
    const response: AxiosResponse<ApiResponse<Order>> = await this.client.put(
      `/orders/${id}`,
      orderData
    );
    return response.data;
  }

  async cancelOrder(id: string): Promise<ApiResponse<void>> {
    const response: AxiosResponse<ApiResponse<void>> = await this.client.delete(
      `/orders/${id}`
    );
    return response.data;
  }

  // Vending Machine endpoints
  async getVendingMachines(
    includeInactive = false
  ): Promise<ApiResponse<VendingMachine[]>> {
    const response: AxiosResponse<ApiResponse<VendingMachine[]>> =
      await this.client.get("/vending-machines", {
        params: { includeInactive },
      });
    return response.data;
  }

  async getVendingMachineById(
    id: string
  ): Promise<ApiResponse<VendingMachine>> {
    const response: AxiosResponse<ApiResponse<VendingMachine>> =
      await this.client.get(`/vending-machines/${id}`);
    return response.data;
  }

  async createVendingMachine(
    machineData: Partial<VendingMachine>
  ): Promise<ApiResponse<VendingMachine>> {
    const response: AxiosResponse<ApiResponse<VendingMachine>> =
      await this.client.post("/vending-machines", machineData);
    return response.data;
  }

  async updateVendingMachine(
    id: string,
    machineData: Partial<VendingMachine>
  ): Promise<ApiResponse<VendingMachine>> {
    const response: AxiosResponse<ApiResponse<VendingMachine>> =
      await this.client.put(`/vending-machines/${id}`, machineData);
    return response.data;
  }

  async deleteVendingMachine(id: string): Promise<ApiResponse<void>> {
    const response: AxiosResponse<ApiResponse<void>> = await this.client.delete(
      `/vending-machines/${id}`
    );
    return response.data;
  }

  async updateMachineStatus(
    id: string,
    status: string,
    telemetry?: Record<string, unknown>
  ): Promise<ApiResponse<void>> {
    const response: AxiosResponse<ApiResponse<void>> = await this.client.post(
      `/vending-machines/${id}/status`,
      {
        status,
        telemetry,
      }
    );
    return response.data;
  }

  async getMachineStatus(id: string): Promise<ApiResponse<unknown>> {
    const response: AxiosResponse<ApiResponse<unknown>> = await this.client.get(
      `/vending-machines/${id}/status`
    );
    return response.data;
  }

  // Payment endpoints
  async createPayment(
    paymentData: Partial<Payment>
  ): Promise<ApiResponse<Payment>> {
    const response: AxiosResponse<ApiResponse<Payment>> =
      await this.client.post("/payments", paymentData);
    return response.data;
  }

  async confirmPayment(id: string): Promise<ApiResponse<Payment>> {
    const response: AxiosResponse<ApiResponse<Payment>> =
      await this.client.post(`/payments/${id}/confirm`);
    return response.data;
  }

  async getPayments(): Promise<ApiResponse<Payment[]>> {
    const response: AxiosResponse<ApiResponse<Payment[]>> =
      await this.client.get("/payments");
    return response.data;
  }

  // Admin Dashboard endpoints
  async getDashboard(): Promise<DashboardData> {
    const response: AxiosResponse<DashboardData> = await this.client.get(
      "/admin/dashboard"
    );
    return response.data;
  }

  async getActiveAlerts(
    severity?: string
  ): Promise<ApiResponse<DashboardAlert[]>> {
    const response: AxiosResponse<ApiResponse<DashboardAlert[]>> =
      await this.client.get("/monitoring/alerts", {
        params: severity ? { severity } : {},
      });
    return response.data;
  }
}

export const apiClient = new ApiClient();
export default apiClient;
