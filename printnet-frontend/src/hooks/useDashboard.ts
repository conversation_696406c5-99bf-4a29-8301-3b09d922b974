import { useState, useEffect } from "react";
import { apiClient } from "@/lib/api";
import type {
  DashboardData,
  SystemMetrics,
  RecentOrder,
  DashboardAlert,
} from "@/types";

interface UseDashboardReturn {
  data: DashboardData | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function useDashboard(): UseDashboardReturn {
  const [data, setData] = useState<DashboardData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDashboardData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Fetch dashboard data from the backend
      const response = await apiClient.getDashboard();

      // The response is already the DashboardData structure
      const transformedData: DashboardData = {
        systemMetrics: response.systemMetrics,
        machineMetrics: response.machineMetrics,
        recentOrders: response.recentOrders || [],
        alerts: response.alerts || [],
      };

      setData(transformedData);
    } catch (err) {
      console.error("Failed to fetch dashboard data:", err);
      setError(
        err instanceof Error ? err.message : "Failed to fetch dashboard data"
      );
    } finally {
      setIsLoading(false);
    }
  };

  const refetch = async () => {
    await fetchDashboardData();
  };

  useEffect(() => {
    fetchDashboardData();
  }, []);

  return {
    data,
    isLoading,
    error,
    refetch,
  };
}

// Helper functions to transform and calculate derived metrics
export function calculateDerivedMetrics(systemMetrics: SystemMetrics) {
  return {
    ordersToday: 0, // This would need to be calculated from a separate endpoint or included in backend
    newUsersToday: 0, // This would need to be calculated from a separate endpoint or included in backend
    machinesNeedingMaintenance: systemMetrics.maintenanceRequired,
  };
}

export function transformRecentOrder(order: RecentOrder) {
  // Get the latest status
  const latestStatus =
    order.statuses && order.statuses.length > 0
      ? order.statuses[order.statuses.length - 1]
      : null;

  return {
    id: order.id,
    customer:
      `${order.user.first_name || ""} ${order.user.last_name || ""}`.trim() ||
      order.user.email,
    status: latestStatus?.status || "unknown",
    machine: order.machine.serial_number,
    cost: order.cost,
    createdAt: order.created_at,
  };
}

export function transformAlert(alert: DashboardAlert) {
  // Map alert types to user-friendly messages and severity
  const alertConfig = {
    OFFLINE: { message: "Machine is offline", severity: "error" as const },
    MAINTENANCE_REQUIRED: {
      message: "Maintenance required",
      severity: "warning" as const,
    },
    LOW_MATERIALS: { message: "Low filament", severity: "warning" as const },
    NO_STATUS: { message: "No status data", severity: "error" as const },
  };

  const config = alertConfig[alert.type as keyof typeof alertConfig] || {
    message: alert.type,
    severity: "warning" as const,
  };

  return {
    id: alert.machineId,
    location: "Unknown Location", // We'll need to fetch machine details separately or include in backend response
    issue: config.message,
    severity: config.severity,
  };
}
